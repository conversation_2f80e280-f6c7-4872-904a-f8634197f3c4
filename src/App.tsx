import React, { useState, useEffect } from 'react';
import { 
  Menu, 
  X, 
  Video, 
  Camera, 
  Film, 
  Monitor, 
  Mic, 
  Users, 
  Calendar, 
  Globe,
  Play,
  ArrowRight,
  Phone,
  Mail,
  MapPin,
  Star,
  Sparkles,
  Zap,
  Award
} from 'lucide-react';

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [isVisible, setIsVisible] = useState({});

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(prev => ({
            ...prev,
            [entry.target.id]: entry.isIntersecting
          }));
        });
      },
      { threshold: 0.1 }
    );

    const elements = document.querySelectorAll('[data-animate]');
    elements.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  const services = [
    {
      icon: <Video className="w-8 h-8" />,
      title: "Company Profiles",
      description: "Professionally crafted videos that showcase your brand's story, values, and achievements.",
      gradient: "from-brand-studio to-brand-martinique"
    },
    {
      icon: <Camera className="w-8 h-8" />,
      title: "Hotel Profiles",
      description: "Visually compelling videos highlighting hotel amenities, ambiance, and guest experiences.",
      gradient: "from-brand-martinique to-brand-studio"
    },
    {
      icon: <Film className="w-8 h-8" />,
      title: "Documentaries",
      description: "In-depth storytelling with powerful visuals to educate, inform, and inspire.",
      gradient: "from-brand-studio to-purple-600"
    },
    {
      icon: <Monitor className="w-8 h-8" />,
      title: "Creative Advertisements",
      description: "Innovative and engaging commercials that leave a lasting impression.",
      gradient: "from-purple-600 to-brand-martinique"
    },
    {
      icon: <Play className="w-8 h-8" />,
      title: "TV Programs",
      description: "Conceptualizing and producing captivating television content.",
      gradient: "from-brand-martinique to-brand-studio"
    },
    {
      icon: <Video className="w-8 h-8" />,
      title: "Short Videos",
      description: "Engaging and impactful short-form content for social media and digital platforms.",
      gradient: "from-brand-studio to-indigo-600"
    },
    {
      icon: <Mic className="w-8 h-8" />,
      title: "Live Event Coverage",
      description: "Seamless and professional broadcasting of concerts, corporate events, and special occasions.",
      gradient: "from-indigo-600 to-brand-martinique"
    },
    {
      icon: <Calendar className="w-8 h-8" />,
      title: "Event Coordination",
      description: "End-to-end planning and execution of media-rich events.",
      gradient: "from-brand-martinique to-purple-600"
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: "Digital Media Solutions",
      description: "Strategic content creation for social media, online marketing, and brand storytelling.",
      gradient: "from-purple-600 to-brand-studio"
    }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      company: "Tech Innovation Corp",
      text: "Their company profile video perfectly captured our brand essence. The quality and storytelling were exceptional.",
      rating: 5
    },
    {
      name: "Michael Chen",
      company: "Grand Plaza Hotel",
      text: "The hotel profile video increased our bookings by 40%. Professional work that truly showcases our property.",
      rating: 5
    },
    {
      name: "Emily Rodriguez",
      company: "Creative Agency",
      text: "Outstanding documentary production. They brought our vision to life with incredible attention to detail.",
      rating: 5
    }
  ];

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false);
  };

  return (
    <div className="min-h-screen bg-brand-soft-peach">
      {/* Floating Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-brand-studio/10 rounded-full animate-float"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-brand-martinique/10 rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-20 w-20 h-20 bg-brand-studio/10 rounded-full animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      {/* Header */}
      <header className="bg-brand-soft-peach/95 backdrop-blur-md shadow-lg sticky top-0 z-50 border-b border-brand-spun-pearl/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center group">
                <div className="relative">
                  <Video className="h-8 w-8 text-brand-studio group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute inset-0 bg-brand-studio/20 rounded-full scale-0 group-hover:scale-150 transition-transform duration-300"></div>
                </div>
                <span className="ml-2 text-xl font-bold text-brand-black group-hover:text-brand-studio transition-colors duration-300">MediaCorp</span>
              </div>
            </div>
            
            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-8">
              {['Home', 'Services', 'About', 'Testimonials', 'Contact'].map((item) => (
                <button 
                  key={item}
                  onClick={() => scrollToSection(item.toLowerCase())}
                  className="relative text-brand-black hover:text-brand-studio transition-colors duration-300 group"
                >
                  {item}
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-studio group-hover:w-full transition-all duration-300"></span>
                </button>
              ))}
            </nav>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-brand-black hover:text-brand-studio transition-colors duration-300 p-2"
              >
                {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden bg-brand-soft-peach/95 backdrop-blur-md border-t border-brand-spun-pearl/20 animate-slide-up">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              {['Home', 'Services', 'About', 'Testimonials', 'Contact'].map((item) => (
                <button 
                  key={item}
                  onClick={() => scrollToSection(item.toLowerCase())}
                  className="block w-full text-left px-3 py-2 text-brand-black hover:text-brand-studio hover:bg-brand-studio/10 transition-all duration-300 rounded-lg"
                >
                  {item}
                </button>
              ))}
            </div>
          </div>
        )}
      </header>

      {/* Hero Section */}
      <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Hero Background Image */}
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url("https://images.pexels.com/photos/7991579/pexels-photo-7991579.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop")',
            transform: `translateY(${scrollY * 0.3}px)`,
          }}
        ></div>
        
        <div 
          className="absolute inset-0 bg-gradient-to-br from-brand-studio/80 via-brand-martinique/70 to-brand-black/90"
          style={{
            transform: `translateY(${scrollY * 0.5}px)`,
          }}
        ></div>
        
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-brand-soft-peach/10 rounded-full blur-3xl animate-pulse-slow"></div>
          <div className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-brand-spun-pearl/10 rounded-full blur-2xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 text-center">
          <div className="animate-fade-in">
            <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold mb-6 leading-tight text-brand-soft-peach animate-slide-up">
              Revolutionizing <span className="text-transparent bg-clip-text bg-gradient-to-r from-brand-soft-peach to-brand-spun-pearl animate-pulse">Media</span> Through Innovation
            </h1>
            
            <p className="text-xl sm:text-2xl mb-8 max-w-4xl mx-auto text-brand-spun-pearl leading-relaxed animate-slide-up" style={{ animationDelay: '0.2s' }}>
              We revolutionize the media industry by delivering visually captivating, high-quality video productions and digital media solutions. Our services combine cutting-edge technology with innovative storytelling to create impactful content that resonates with audiences.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up" style={{ animationDelay: '0.4s' }}>
              <button 
                onClick={() => scrollToSection('services')}
                className="group bg-gradient-to-r from-brand-soft-peach to-brand-spun-pearl text-brand-black px-8 py-4 rounded-full font-semibold hover:shadow-2xl hover:scale-105 transition-all duration-300 flex items-center justify-center"
              >
                Our Services 
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
              </button>
              <button 
                onClick={() => scrollToSection('contact')}
                className="group border-2 border-brand-soft-peach text-brand-soft-peach px-8 py-4 rounded-full font-semibold hover:bg-brand-soft-peach hover:text-brand-black transition-all duration-300 hover:scale-105"
              >
                Get Started
              </button>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-brand-soft-peach rounded-full flex justify-center">
            <div className="w-1 h-3 bg-brand-soft-peach rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* TV Channel Showcase Section */}
      <section className="py-20 bg-gradient-to-br from-brand-soft-peach to-brand-spun-pearl/20 relative overflow-hidden" data-animate>
        <div className="absolute inset-0">
          <div className="absolute top-20 right-20 w-40 h-40 bg-brand-studio/5 rounded-full blur-2xl animate-float"></div>
          <div className="absolute bottom-20 left-20 w-32 h-32 bg-brand-martinique/5 rounded-full blur-xl animate-float" style={{ animationDelay: '2s' }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className={`${isVisible.services ? 'animate-slide-in-left' : 'opacity-0'}`}>
              <div className="flex items-center mb-6">
                <Monitor className="w-12 h-12 text-brand-studio mr-4 animate-pulse" />
                <h2 className="text-3xl sm:text-4xl font-bold text-brand-black">Our TV Channel</h2>
              </div>
              
              <h3 className="text-2xl font-semibold text-brand-martinique mb-4">MediaCorp Broadcasting Network</h3>
              
              <p className="text-lg text-brand-martinique mb-6 leading-relaxed">
                Experience premium content through our dedicated television channel, featuring original documentaries, 
                corporate showcases, and innovative programming. We broadcast high-quality content that informs, 
                entertains, and inspires audiences across multiple platforms.
              </p>
              
              <div className="space-y-4 mb-8">
                {[
                  { icon: Film, title: "Original Documentaries", desc: "Award-winning documentary series covering diverse topics" },
                  { icon: Video, title: "Corporate Features", desc: "In-depth profiles of leading companies and entrepreneurs" },
                  { icon: Globe, title: "Global Reach", desc: "Broadcasting to audiences worldwide through digital platforms" }
                ].map((item, index) => (
                  <div key={index} className="flex items-start group">
                    <div className="bg-brand-studio/10 rounded-full p-3 mr-4 group-hover:bg-brand-studio/20 transition-colors duration-300">
                      <item.icon className="h-5 w-5 text-brand-studio group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-brand-black mb-1 group-hover:text-brand-studio transition-colors duration-300">
                        {item.title}
                      </h4>
                      <p className="text-brand-martinique text-sm group-hover:text-brand-black transition-colors duration-300">
                        {item.desc}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
              
              <button 
                onClick={() => scrollToSection('contact')}
                className="group bg-gradient-to-r from-brand-studio to-brand-martinique text-brand-soft-peach px-8 py-4 rounded-full font-semibold hover:shadow-2xl hover:scale-105 transition-all duration-300 flex items-center"
              >
                Learn More About Our Channel
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
              </button>
            </div>
            
            <div className={`${isVisible.services ? 'animate-slide-in-right' : 'opacity-0'}`}>
              <div className="relative bg-gradient-to-br from-brand-black to-brand-martinique rounded-2xl p-8 shadow-2xl hover:shadow-3xl transition-shadow duration-500 group overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-brand-studio/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                <div className="relative z-10">
                  <div className="aspect-video bg-brand-soft-peach/10 rounded-xl mb-6 flex items-center justify-center relative overflow-hidden group-hover:scale-105 transition-transform duration-500">
                    {/* Video Preview Placeholder */}
                    <div className="absolute inset-0 bg-gradient-to-br from-brand-studio/20 to-brand-martinique/20"></div>
                    <div className="relative z-10 text-center">
                      <div className="bg-brand-soft-peach/20 rounded-full p-6 mb-4 inline-block backdrop-blur-sm">
                        <Play className="w-12 h-12 text-brand-soft-peach" />
                      </div>
                      <p className="text-brand-soft-peach font-medium">Channel Preview</p>
                      <p className="text-brand-spun-pearl text-sm mt-1">Click to watch our latest broadcast</p>
                    </div>
                    
                    {/* Animated border */}
                    <div className="absolute inset-0 border-2 border-brand-studio/30 rounded-xl animate-pulse"></div>
                  </div>
                  
                  <div className="text-center">
                    <h4 className="text-xl font-bold text-brand-soft-peach mb-2">Latest Episode</h4>
                    <p className="text-brand-spun-pearl mb-4">"Innovation in Modern Business" - A deep dive into how technology is reshaping corporate landscapes</p>
                    <div className="flex items-center justify-center space-x-4 text-sm text-brand-spun-pearl">
                      <span className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        Dec 2024
                      </span>
                      <span className="flex items-center">
                        <Users className="w-4 h-4 mr-1" />
                        45min
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-brand-soft-peach relative overflow-hidden" data-animate>
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-brand-spun-pearl/5 to-transparent"></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`text-center mb-16 ${isVisible.services ? 'animate-slide-up' : 'opacity-0'}`}>
            <div className="flex justify-center mb-4">
              <Zap className="w-12 h-12 text-brand-studio animate-pulse" />
            </div>
            <h2 className="text-3xl sm:text-5xl font-bold text-brand-black mb-4">Our Services</h2>
            <p className="text-xl text-brand-martinique max-w-3xl mx-auto">
              We specialize in high-quality video productions and digital media solutions, offering a range of services tailored to engage and inspire audiences.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div 
                key={index}
                className={`group relative bg-brand-soft-peach rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 p-6 border border-brand-spun-pearl/20 hover:scale-105 overflow-hidden ${
                  isVisible.services ? 'animate-slide-up' : 'opacity-0'
                }`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}></div>
                
                <div className="relative z-10">
                  <div className="text-brand-studio mb-4 group-hover:scale-110 transition-transform duration-300">
                    {service.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-brand-black mb-3 group-hover:text-brand-studio transition-colors duration-300">
                    {service.title}
                  </h3>
                  <p className="text-brand-martinique leading-relaxed group-hover:text-brand-black transition-colors duration-300">
                    {service.description}
                  </p>
                </div>

                <div className="absolute top-0 right-0 w-20 h-20 bg-brand-studio/5 rounded-full -translate-y-10 translate-x-10 group-hover:scale-150 transition-transform duration-500"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 bg-gradient-to-br from-brand-martinique to-brand-black text-brand-soft-peach relative overflow-hidden" data-animate>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-40 h-40 bg-brand-studio/10 rounded-full blur-2xl animate-float"></div>
          <div className="absolute bottom-20 right-20 w-32 h-32 bg-brand-spun-pearl/10 rounded-full blur-xl animate-float" style={{ animationDelay: '3s' }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className={`${isVisible.about ? 'animate-slide-in-left' : 'opacity-0'}`}>
              <div className="flex items-center mb-6">
                <Award className="w-12 h-12 text-brand-studio mr-4 animate-pulse" />
                <h2 className="text-3xl sm:text-5xl font-bold">Why Choose MediaCorp?</h2>
              </div>
              
              <p className="text-lg text-brand-spun-pearl mb-8 leading-relaxed">
                With years of experience in the media industry, we understand what it takes to create content that not only looks professional but also drives results. Our team combines technical expertise with creative vision to deliver exceptional outcomes.
              </p>
              
              <div className="space-y-6">
                {[
                  { icon: Users, title: "Expert Team", desc: "Skilled professionals with extensive industry experience" },
                  { icon: Monitor, title: "Cutting-Edge Technology", desc: "Latest equipment and software for superior quality" },
                  { icon: Star, title: "Proven Results", desc: "Track record of successful projects and satisfied clients" }
                ].map((item, index) => (
                  <div key={index} className="flex items-start group">
                    <div className="bg-brand-studio/20 rounded-full p-3 mr-4 group-hover:bg-brand-studio/30 transition-colors duration-300">
                      <item.icon className="h-6 w-6 text-brand-studio group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-brand-soft-peach mb-1 group-hover:text-brand-spun-pearl transition-colors duration-300">
                        {item.title}
                      </h3>
                      <p className="text-brand-spun-pearl group-hover:text-brand-soft-peach transition-colors duration-300">
                        {item.desc}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className={`${isVisible.about ? 'animate-slide-in-right' : 'opacity-0'}`}>
              <div className="relative bg-gradient-to-br from-brand-studio to-brand-martinique rounded-2xl p-8 shadow-2xl hover:shadow-3xl transition-shadow duration-500 group">
                <div className="absolute inset-0 bg-gradient-to-br from-brand-soft-peach/10 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                <div className="relative z-10">
                  <h3 className="text-2xl font-bold mb-4 flex items-center">
                    <Sparkles className="w-8 h-8 mr-3 animate-pulse" />
                    Ready to Transform Your Brand?
                  </h3>
                  <p className="text-brand-spun-pearl mb-6 leading-relaxed">
                    Let's discuss how our media solutions can help your business stand out in today's competitive market. We're committed to delivering content that exceeds expectations.
                  </p>
                  <button 
                    onClick={() => scrollToSection('contact')}
                    className="bg-brand-soft-peach text-brand-black px-6 py-3 rounded-full font-semibold hover:bg-brand-spun-pearl hover:scale-105 transition-all duration-300 shadow-lg"
                  >
                    Start Your Project
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 bg-brand-soft-peach relative overflow-hidden" data-animate>
        <div className="absolute inset-0 bg-gradient-to-r from-brand-spun-pearl/5 via-transparent to-brand-spun-pearl/5"></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`text-center mb-16 ${isVisible.testimonials ? 'animate-slide-up' : 'opacity-0'}`}>
            <h2 className="text-3xl sm:text-5xl font-bold text-brand-black mb-4">What Our Clients Say</h2>
            <p className="text-xl text-brand-martinique">Don't just take our word for it - hear from our satisfied clients</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div 
                key={index} 
                className={`group bg-brand-soft-peach rounded-2xl shadow-lg hover:shadow-2xl p-6 border border-brand-spun-pearl/20 hover:scale-105 transition-all duration-500 relative overflow-hidden ${
                  isVisible.testimonials ? 'animate-slide-up' : 'opacity-0'
                }`}
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-brand-studio/5 to-brand-martinique/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                <div className="relative z-10">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-brand-studio fill-current animate-pulse" style={{ animationDelay: `${i * 0.1}s` }} />
                    ))}
                  </div>
                  <p className="text-brand-martinique mb-4 italic leading-relaxed group-hover:text-brand-black transition-colors duration-300">
                    "{testimonial.text}"
                  </p>
                  <div>
                    <h4 className="font-semibold text-brand-black group-hover:text-brand-studio transition-colors duration-300">
                      {testimonial.name}
                    </h4>
                    <p className="text-brand-spun-pearl">{testimonial.company}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-gradient-to-br from-brand-black to-brand-martinique text-brand-soft-peach relative overflow-hidden" data-animate>
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-32 h-32 bg-brand-studio/10 rounded-full blur-xl animate-float"></div>
          <div className="absolute bottom-10 right-10 w-24 h-24 bg-brand-spun-pearl/10 rounded-full blur-lg animate-float" style={{ animationDelay: '2s' }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`text-center mb-16 ${isVisible.contact ? 'animate-slide-up' : 'opacity-0'}`}>
            <h2 className="text-3xl sm:text-5xl font-bold mb-4">Get In Touch</h2>
            <p className="text-xl text-brand-spun-pearl">Ready to start your next project? We'd love to hear from you.</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className={`${isVisible.contact ? 'animate-slide-in-left' : 'opacity-0'}`}>
              <h3 className="text-2xl font-semibold mb-6 flex items-center">
                <Phone className="w-8 h-8 mr-3 text-brand-studio animate-pulse" />
                Contact Information
              </h3>
              <div className="space-y-6">
                {[
                  { icon: Phone, title: "Phone", info: "+****************" },
                  { icon: Mail, title: "Email", info: "<EMAIL>" },
                  { icon: MapPin, title: "Address", info: "123 Media Street\nCreative District, CD 12345" }
                ].map((contact, index) => (
                  <div key={index} className="flex items-start group">
                    <contact.icon className="h-6 w-6 text-brand-studio mr-4 mt-1 group-hover:scale-110 transition-transform duration-300" />
                    <div>
                      <h4 className="font-semibold text-brand-soft-peach group-hover:text-brand-spun-pearl transition-colors duration-300">
                        {contact.title}
                      </h4>
                      <p className="text-brand-spun-pearl whitespace-pre-line group-hover:text-brand-soft-peach transition-colors duration-300">
                        {contact.info}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className={`${isVisible.contact ? 'animate-slide-in-right' : 'opacity-0'}`}>
              <h3 className="text-2xl font-semibold mb-6 flex items-center">
                <Mail className="w-8 h-8 mr-3 text-brand-studio animate-pulse" />
                Send Us a Message
              </h3>
              <form className="space-y-4">
                {[
                  { id: "name", label: "Name", type: "text", placeholder: "Your Name" },
                  { id: "email", label: "Email", type: "email", placeholder: "<EMAIL>" }
                ].map((field) => (
                  <div key={field.id}>
                    <label htmlFor={field.id} className="block text-sm font-medium text-brand-spun-pearl mb-1">
                      {field.label}
                    </label>
                    <input 
                      type={field.type} 
                      id={field.id} 
                      className="w-full px-4 py-3 border border-brand-spun-pearl/30 rounded-lg focus:ring-2 focus:ring-brand-studio focus:border-brand-studio transition-all duration-300 bg-brand-soft-peach/10 backdrop-blur-sm text-brand-soft-peach placeholder-brand-spun-pearl"
                      placeholder={field.placeholder}
                    />
                  </div>
                ))}
                
                <div>
                  <label htmlFor="service" className="block text-sm font-medium text-brand-spun-pearl mb-1">Service Interest</label>
                  <select 
                    id="service" 
                    className="w-full px-4 py-3 border border-brand-spun-pearl/30 rounded-lg focus:ring-2 focus:ring-brand-studio focus:border-brand-studio transition-all duration-300 bg-brand-soft-peach/10 backdrop-blur-sm text-brand-soft-peach"
                  >
                    <option value="">Select a service</option>
                    {services.map((service, index) => (
                      <option key={index} value={service.title.toLowerCase().replace(/\s+/g, '-')} className="text-brand-black">
                        {service.title}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-brand-spun-pearl mb-1">Message</label>
                  <textarea 
                    id="message" 
                    rows={4}
                    className="w-full px-4 py-3 border border-brand-spun-pearl/30 rounded-lg focus:ring-2 focus:ring-brand-studio focus:border-brand-studio transition-all duration-300 bg-brand-soft-peach/10 backdrop-blur-sm text-brand-soft-peach placeholder-brand-spun-pearl"
                    placeholder="Tell us about your project..."
                  ></textarea>
                </div>
                
                <button 
                  type="submit"
                  className="w-full bg-gradient-to-r from-brand-studio to-brand-martinique text-brand-soft-peach px-6 py-3 rounded-lg font-semibold hover:shadow-2xl hover:scale-105 transition-all duration-300"
                >
                  Send Message
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-brand-black text-brand-soft-peach py-12 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-t from-brand-martinique/20 to-transparent"></div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="animate-fade-in">
              <div className="flex items-center mb-4">
                <Video className="h-8 w-8 text-brand-studio animate-pulse" />
                <span className="ml-2 text-xl font-bold">MediaCorp</span>
              </div>
              <p className="text-brand-spun-pearl leading-relaxed">
                Revolutionizing the media industry through innovative video production and digital solutions.
              </p>
            </div>
            
            {[
              {
                title: "Services",
                links: ["Video Production", "Documentaries", "Event Coverage", "Digital Media"]
              },
              {
                title: "Company", 
                links: ["About Us", "Our Team", "Careers", "Portfolio"]
              },
              {
                title: "Contact",
                links: ["123 Media Street", "Creative District, CD 12345", "+****************", "<EMAIL>"]
              }
            ].map((section, index) => (
              <div key={index} className="animate-fade-in" style={{ animationDelay: `${(index + 1) * 0.1}s` }}>
                <h4 className="text-lg font-semibold mb-4 text-brand-studio">{section.title}</h4>
                <ul className="space-y-2 text-brand-spun-pearl">
                  {section.links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <a href="#" className="hover:text-brand-soft-peach transition-colors duration-300 hover:translate-x-1 inline-block">
                        {link}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
          
          <div className="border-t border-brand-martinique/30 mt-8 pt-8 text-center text-brand-spun-pearl animate-fade-in">
            <p>&copy; 2024 MediaCorp. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;